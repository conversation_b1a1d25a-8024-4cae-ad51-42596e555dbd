/* Role Custom Admin Para <PERSON><PERSON> - Modern Tasarım */

.role-custom-admin-withdraw {
    margin: 0;
    padding: 0;
    background: #f1f1f1;
    min-height: 100vh;
}

/* Header <PERSON>ümü */
.role-custom-withdraw-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 40px;
    margin: 0 0 0 -20px;
    margin-top: -10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.role-custom-withdraw-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.role-custom-withdraw-title .dashicons {
    font-size: 32px;
    margin-right: 15px;
    opacity: 0.9;
}

.role-custom-withdraw-title h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 300;
    color: white;
}

/* <PERSON><PERSON><PERSON> */
.role-custom-withdraw-summary {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.summary-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    min-width: 120px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.summary-card:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.summary-number {
    display: block;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.summary-label {
    display: block;
    font-size: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* İçerik Bölümü */
.role-custom-withdraw-content {
    margin: 0 20px;
    padding: 30px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: -20px;
    position: relative;
    z-index: 1;
}

/* Filter Tabs */
.role-custom-filter-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 30px;
    border-bottom: 1px solid #e1e1e1;
}

.filter-tab {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    text-decoration: none;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
}

.filter-tab:hover {
    color: #0073aa;
    background: #f8f9fa;
}

.filter-tab.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
    background: #f8f9fa;
}

.filter-tab .dashicons {
    margin-right: 8px;
    font-size: 16px;
}

.tab-count {
    background: #e1e1e1;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    margin-left: 8px;
    font-weight: 600;
}

.filter-tab.active .tab-count {
    background: #0073aa;
    color: white;
}

/* Tablo Container */
.role-custom-withdraw-table-container {
    margin-top: 20px;
}

.role-custom-table-wrapper {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-custom-withdraw-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.role-custom-withdraw-table thead th {
    background: #f8f9fa;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-custom-withdraw-table thead th .dashicons {
    margin-right: 6px;
    opacity: 0.7;
}

.withdraw-requests-table tr:hover {
    background-color: #f5f5f5;
}

/* Durum Badge'leri */
.withdraw-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.withdraw-status.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.withdraw-status.approved {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.withdraw-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Tablo Satırları */
.withdraw-row {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.withdraw-row:hover {
    background-color: #f8f9fa;
}

.withdraw-row td {
    padding: 16px;
    vertical-align: middle;
}

/* Eğitmen Bilgileri */
.instructor-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.instructor-avatar img {
    border-radius: 50%;
    border: 2px solid #e9ecef;
}

.instructor-details {
    display: flex;
    flex-direction: column;
}

.instructor-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.instructor-email {
    font-size: 12px;
    color: #6c757d;
}

/* Miktar Gösterimi */
.amount-display {
    display: flex;
    align-items: baseline;
    gap: 4px;
}

.amount-value {
    font-size: 18px;
    font-weight: 600;
    color: #28a745;
}

.amount-currency {
    font-size: 14px;
    color: #6c757d;
}

/* Tarih Gösterimi */
.date-display {
    display: flex;
    flex-direction: column;
}

.date-value {
    font-weight: 500;
    color: #495057;
}

.time-value {
    font-size: 12px;
    color: #6c757d;
}

/* Hesap Bilgileri */
.account-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.bank-name {
    font-weight: 600;
    color: #495057;
}

.account-holder {
    color: #6c757d;
    font-size: 13px;
}

.iban-info {
    font-family: monospace;
    font-size: 11px;
    color: #6c757d;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

.no-account-info {
    color: #6c757d;
    font-style: italic;
}

.withdraw-status.unknown {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* İşlem Butonları */
.action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-approve,
.btn-reject,
.btn-delete {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-approve {
    background: #28a745;
    color: white;
}

.btn-approve:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-reject {
    background: #dc3545;
    color: white;
}

.btn-reject:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-delete {
    background: #6c757d;
    color: white;
}

.btn-delete:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.action-completed {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: #28a745;
    font-size: 12px;
    font-weight: 500;
}

.action-completed .dashicons {
    font-size: 16px;
}

/* Empty State */
.role-custom-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    margin-bottom: 20px;
}

.empty-icon .dashicons {
    font-size: 64px;
    opacity: 0.3;
}

.role-custom-empty-state h3 {
    margin: 0 0 10px 0;
    color: #495057;
    font-weight: 500;
}

.role-custom-empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Note Row */
.note-row td {
    background: #f8f9fa;
    border-top: none;
    padding: 12px 16px;
}

.request-note {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 13px;
}

.note-icon {
    color: #6c757d;
    margin-top: 2px;
}

.note-text {
    color: #495057;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .role-custom-withdraw-summary {
        gap: 15px;
    }

    .summary-card {
        min-width: 100px;
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .role-custom-withdraw-header {
        padding: 20px;
        margin-left: -10px;
    }

    .role-custom-withdraw-content {
        margin: 0 10px;
        padding: 20px;
    }

    .role-custom-filter-tabs {
        flex-wrap: wrap;
    }

    .filter-tab {
        padding: 12px 15px;
    }

    .instructor-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }

    .role-custom-withdraw-table {
        font-size: 13px;
    }

    .role-custom-withdraw-table th,
    .role-custom-withdraw-table td {
        padding: 12px 8px;
    }
}

/* Loading Durumu */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Boş Durum */
.no-requests {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.no-requests p {
    margin: 0;
    font-size: 16px;
}

/* Hesap Bilgileri */
.withdraw-requests-table td small {
    color: #666;
    font-size: 12px;
}

.withdraw-requests-table td strong {
    color: #1d2327;
}

/* Responsive */
@media (max-width: 768px) {
    .withdraw-requests-table {
        overflow-x: auto;
    }
    
    .withdraw-requests-table table {
        min-width: 800px;
    }
    
    .withdraw-requests-table th,
    .withdraw-requests-table td {
        padding: 8px;
        font-size: 13px;
    }
    
    .approve-withdraw,
    .reject-withdraw {
        display: block;
        width: 100%;
        margin: 2px 0;
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Başarı ve Hata Mesajları */
.role-custom-admin-notice {
    padding: 12px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.role-custom-admin-notice.success {
    background-color: #d1edff;
    border-left-color: #00a32a;
    color: #00a32a;
}

.role-custom-admin-notice.error {
    background-color: #f8d7da;
    border-left-color: #d63638;
    color: #d63638;
}

/* Tablo Sıralama */
.withdraw-requests-table th.sortable {
    cursor: pointer;
    position: relative;
}

.withdraw-requests-table th.sortable:hover {
    background-color: #f0f0f1;
}

.withdraw-requests-table th.sortable::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #999;
}

.withdraw-requests-table th.sortable.desc::after {
    border-bottom: none;
    border-top: 4px solid #999;
}

/* Miktar Vurgusu */
.withdraw-requests-table .amount {
    font-weight: 600;
    color: #2271b1;
    font-size: 14px;
}

/* Tarih Formatı */
.withdraw-requests-table .date {
    color: #666;
    font-size: 13px;
}

/* Kullanıcı Bilgileri */
.withdraw-requests-table .user-info strong {
    display: block;
    margin-bottom: 2px;
}

.withdraw-requests-table .user-info small {
    color: #666;
}

/* Hesap Detayları */
.withdraw-requests-table .account-details {
    font-size: 12px;
    line-height: 1.4;
}

.withdraw-requests-table .account-details strong {
    display: block;
    margin-bottom: 2px;
    color: #1d2327;
}

/* Not Satırı */
.withdraw-requests-table .note-row {
    background-color: #f9f9f9;
    font-style: italic;
}

.withdraw-requests-table .note-row td {
    padding: 8px 12px;
    border-top: none;
    color: #666;
}

/* İşlem Tamamlandı Durumu */
.withdraw-requests-table .completed {
    color: #666;
    font-style: italic;
    font-size: 12px;
}
